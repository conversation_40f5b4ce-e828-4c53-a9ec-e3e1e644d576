<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Medallia VOC系统分析</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
            overflow: hidden;
        }

        .container {
            max-width: 1200px;
            width: 100%;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #1a202c;
            margin-bottom: 16px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            font-size: 1.1rem;
            color: #4a5568;
            line-height: 1.6;
            max-width: 800px;
            margin: 0 auto;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 24px;
            margin-top: 40px;
        }

        .feature-card {
            background: white;
            border-radius: 16px;
            padding: 24px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
            border: 1px solid rgba(0, 0, 0, 0.05);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(90deg, #667eea, #764ba2);
        }

        .feature-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
        }

        .feature-icon {
            width: 48px;
            height: 48px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 12px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;
            font-size: 24px;
            color: white;
        }

        .feature-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #1a202c;
            margin-bottom: 12px;
            line-height: 1.4;
        }

        .feature-description {
            color: #4a5568;
            line-height: 1.6;
            font-size: 0.95rem;
        }

        @media (max-width: 768px) {
            .container {
                padding: 24px;
                margin: 10px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 16px;
            }
            
            .feature-card {
                padding: 20px;
            }
        }

        @media (max-height: 800px) {
            .container {
                padding: 30px;
            }
            
            .header {
                margin-bottom: 30px;
            }
            
            .title {
                font-size: 2.2rem;
                margin-bottom: 12px;
            }
            
            .subtitle {
                font-size: 1rem;
            }
            
            .features-grid {
                gap: 20px;
                margin-top: 30px;
            }
            
            .feature-card {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">Medallia在VOC系统与解决方案上的核心功能</h1>
            <p class="subtitle">Medallia的VOC系统通过其综合性的功能组合，帮助企业从简单的反馈收集发展为智能化的客户体验管理</p>
        </div>

        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-icon">🌐</div>
                <h3 class="feature-title">全渠道数据采集</h3>
                <p class="feature-description">支持从多种渠道收集客户反馈，包括网站、移动应用、社交媒体、联络中心等数字化和传统接触点。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🎤</div>
                <h3 class="feature-title">语音分析功能</h3>
                <p class="feature-description">支持实时的语音转录、情感分析以及对数百万通话时间的分析。这使企业能够将语音洞察与文本分析、数字渠道等其他数据源结合，获得端到端客户旅程的全面理解。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📝</div>
                <h3 class="feature-title">文本分析引擎</h3>
                <p class="feature-description">能够自动发现传统文本分析方法可能遗漏的客户反馈主题，可以实时处理数十亿条客户评论，识别隐藏的趋势和话题。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">⚡</div>
                <h3 class="feature-title">实时警报系统</h3>
                <p class="feature-description">平台提供实时警报功能，使团队能够快速响应并主动处理潜在问题。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">🤖</div>
                <h3 class="feature-title">实时行动与自动化</h3>
                <p class="feature-description">通过分析评分和评论，识别、分类并排序需要关注的客户。</p>
            </div>

            <div class="feature-card">
                <div class="feature-icon">📊</div>
                <h3 class="feature-title">全渠道统一视图</h3>
                <p class="feature-description">支持在单一仪表板中管理数字和非数字渠道，提供客户体验的全方位视图。这种统一的方法使组织能够在所有客户数据之间建立连接。</p>
            </div>
        </div>
    </div>
</body>
</html>
