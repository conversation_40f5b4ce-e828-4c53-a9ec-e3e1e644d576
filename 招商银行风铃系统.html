<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招商银行风铃系统 - 零售客户体验监测平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #c31432 0%, #240b36 100%);
            min-height: 100vh;
            color: #333;
            overflow-x: hidden;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
            border-radius: 0 0 32px 32px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
            min-height: 100vh;
        }

        .header {
            background: linear-gradient(135deg, #c31432, #240b36);
            color: white;
            padding: 60px 40px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .logo {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 20px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1.3rem;
            opacity: 0.9;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }

        .main-content {
            padding: 50px 40px;
        }

        .overview {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 50px;
            border-left: 6px solid #c31432;
        }

        .overview h2 {
            color: #c31432;
            font-size: 2rem;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .overview-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: center;
        }

        .overview-text {
            font-size: 1.1rem;
            line-height: 1.8;
            color: #495057;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .stat-item {
            background: white;
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #c31432;
            display: block;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .features-section {
            margin-bottom: 50px;
        }

        .section-title {
            font-size: 2.2rem;
            color: #240b36;
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .section-title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 4px;
            background: linear-gradient(90deg, #c31432, #240b36);
            border-radius: 2px;
        }

        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 30px;
        }

        .feature-card {
            background: white;
            border-radius: 20px;
            padding: 35px;
            box-shadow: 0 8px 30px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(195, 20, 50, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 5px;
            background: linear-gradient(90deg, #c31432, #240b36);
        }

        .feature-card:hover {
            transform: translateY(-8px);
            box-shadow: 0 15px 40px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, #c31432, #240b36);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            font-size: 28px;
            color: white;
            box-shadow: 0 4px 15px rgba(195, 20, 50, 0.3);
        }

        .feature-title {
            font-size: 1.4rem;
            font-weight: 600;
            color: #240b36;
            margin-bottom: 15px;
            line-height: 1.4;
        }

        .feature-description {
            color: #495057;
            line-height: 1.7;
            font-size: 1rem;
            margin-bottom: 15px;
        }

        .feature-highlight {
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #c31432;
            font-size: 0.9rem;
            color: #721c24;
        }

        .architecture {
            background: linear-gradient(135deg, #240b36, #c31432);
            color: white;
            padding: 50px 40px;
            border-radius: 25px;
            margin-bottom: 50px;
        }

        .architecture h2 {
            text-align: center;
            font-size: 2.2rem;
            margin-bottom: 40px;
        }

        .architecture-content {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 25px;
        }

        .arch-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 25px;
            border-radius: 15px;
            text-align: center;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .arch-item h3 {
            margin-bottom: 15px;
            font-size: 1.2rem;
        }

        @media (max-width: 768px) {
            .container {
                border-radius: 0;
            }
            
            .header, .main-content, .architecture {
                padding: 30px 20px;
            }
            
            .overview-content {
                grid-template-columns: 1fr;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .feature-card {
                padding: 25px;
            }
            
            .logo {
                font-size: 2.2rem;
            }
            
            .subtitle {
                font-size: 1.1rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1 class="logo">🔔 风铃系统</h1>
                <p class="subtitle">招商银行零售客户体验监测平台 - 以客户为中心的数字化体验管理系统</p>
            </div>
        </div>

        <div class="main-content">
            <div class="overview">
                <h2>🎯 系统概览</h2>
                <div class="overview-content">
                    <div class="overview-text">
                        风铃系统是招商银行于2019年搭建的零售客户体验监测平台，从客户全旅程视角出发，打通银行内部20多个系统，实现客户体验的实时监测和数字化管理。系统将客户体验的度量由以往主观、定性的评价变成客观、定量、实时的数据分析。
                    </div>
                    <div class="overview-stats">
                        <div class="stat-item">
                            <span class="stat-number">20+</span>
                            <div class="stat-label">打通系统数量</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3万+</span>
                            <div class="stat-label">埋点数据</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">1200+</span>
                            <div class="stat-label">体验指标</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">实时</span>
                            <div class="stat-label">监测能力</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="features-section">
                <h2 class="section-title">🌟 核心功能亮点</h2>
                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔗</div>
                        <h3 class="feature-title">全系统数据打通</h3>
                        <p class="feature-description">
                            整合银行内部20多个业务系统，实现数据的统一汇聚和分析，打破系统间的数据孤岛，形成完整的客户体验数据链路。
                        </p>
                        <div class="feature-highlight">
                            <strong>技术亮点：</strong> 通过API接口和数据中台技术，实现跨系统数据实时同步
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">全旅程体验监测</h3>
                        <p class="feature-description">
                            从客户视角出发，监测客户在银行各个触点的完整体验旅程，包括线上线下全渠道的交互行为和体验感受。
                        </p>
                        <div class="feature-highlight">
                            <strong>覆盖范围：</strong> 手机银行、网银、ATM、网点、客服等全渠道触点
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">实时数据埋点</h3>
                        <p class="feature-description">
                            部署3万余个数据埋点，实时采集客户行为数据、交易数据、服务数据等，确保体验监测的及时性和准确性。
                        </p>
                        <div class="feature-highlight">
                            <strong>数据类型：</strong> 行为埋点、业务埋点、性能埋点、异常埋点
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <h3 class="feature-title">多维度指标体系</h3>
                        <p class="feature-description">
                            构建1200余项体验指标，涵盖功能可用性、系统性能、服务质量、客户满意度等多个维度，形成立体化的体验评价体系。
                        </p>
                        <div class="feature-highlight">
                            <strong>核心指标：</strong> NPS净推荐值、CES客户费力度、CSAT客户满意度
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🎛️</div>
                        <h3 class="feature-title">可视化监测仪表盘</h3>
                        <p class="feature-description">
                            提供直观的数据可视化界面，实时展示客户体验状况，支持多维度数据钻取和分析，帮助管理层快速决策。
                        </p>
                        <div class="feature-highlight">
                            <strong>展示能力：</strong> 实时大屏、趋势分析、异常预警、对比分析
                        </div>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔄</div>
                        <h3 class="feature-title">闭环管理机制</h3>
                        <p class="feature-description">
                            建立"监测-分析-改进"的完整闭环，从发现问题到分析原因再到改进措施，形成持续优化的体验管理流程。
                        </p>
                        <div class="feature-highlight">
                            <strong>管理流程：</strong> 问题识别 → 根因分析 → 改进方案 → 效果验证
                        </div>
                    </div>
                </div>
            </div>

            <div class="architecture">
                <h2>🏗️ 技术架构</h2>
                <div class="architecture-content">
                    <div class="arch-item">
                        <h3>数据采集层</h3>
                        <p>多渠道数据埋点<br>实时数据采集<br>数据清洗处理</p>
                    </div>
                    <div class="arch-item">
                        <h3>数据存储层</h3>
                        <p>大数据平台<br>实时数据库<br>历史数据仓库</p>
                    </div>
                    <div class="arch-item">
                        <h3>分析计算层</h3>
                        <p>实时计算引擎<br>机器学习算法<br>统计分析模型</p>
                    </div>
                    <div class="arch-item">
                        <h3>应用展示层</h3>
                        <p>可视化仪表盘<br>预警通知系统<br>报表分析工具</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
