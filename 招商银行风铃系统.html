<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招商银行风铃系统 - 零售客户体验监测平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #c31432 0%, #240b36 100%);
            height: 100vh;
            color: #333;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: grid;
            grid-template-rows: auto 1fr;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
        }

        .header {
            background: linear-gradient(135deg, #c31432, #240b36);
            color: white;
            padding: 20px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .logo {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .main-content {
            padding: 20px 30px;
            height: calc(100vh - 120px);
            overflow-y: auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .overview {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #c31432;
        }

        .overview h2 {
            color: #c31432;
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .stat-item {
            background: white;
            padding: 15px 10px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #c31432;
            display: block;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.75rem;
            margin-top: 3px;
        }

        .ui-mockup {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(195, 20, 50, 0.1);
        }

        .ui-mockup h3 {
            color: #c31432;
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dashboard-mockup {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 15px;
            border: 2px dashed #c31432;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .dashboard-mockup .chart-placeholder {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #c31432, #240b36);
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .experts-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .experts-section h3 {
            color: #c31432;
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .expert-quote {
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #c31432;
            margin-bottom: 15px;
        }

        .expert-quote .quote-text {
            font-style: italic;
            color: #495057;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .expert-quote .quote-author {
            font-weight: 600;
            color: #c31432;
            font-size: 0.85rem;
        }

        .differentiators {
            background: linear-gradient(135deg, #240b36, #c31432);
            color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }

        .differentiators h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .diff-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .diff-item h4 {
            font-size: 1rem;
            margin-bottom: 5px;
            color: #fff;
        }

        .diff-item p {
            font-size: 0.85rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(195, 20, 50, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #c31432, #240b36);
        }

        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #c31432, #240b36);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 18px;
            color: white;
            box-shadow: 0 2px 8px rgba(195, 20, 50, 0.3);
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            color: #240b36;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .feature-description {
            color: #495057;
            line-height: 1.5;
            font-size: 0.85rem;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: 15px 20px;
            }

            .header {
                padding: 15px 20px;
            }

            .logo {
                font-size: 1.8rem;
            }

            .subtitle {
                font-size: 0.9rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1 class="logo">🔔 风铃系统</h1>
                <p class="subtitle">招商银行一站式AIOps智能运维平台 - "风起于青萍之末，铃响于未然之时"</p>
            </div>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <div class="overview">
                    <h2>🎯 系统概览</h2>
                    <div class="overview-stats">
                        <div class="stat-item">
                            <span class="stat-number">数千</span>
                            <div class="stat-label">应用系统</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">金融级</span>
                            <div class="stat-label">严苛标准</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">全栈</span>
                            <div class="stat-label">自主研发</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">AIOps</span>
                            <div class="stat-label">智能运维</div>
                        </div>
                    </div>
                </div>

                <div class="ui-mockup">
                    <h3>📱 用户界面展示</h3>
                    <div class="dashboard-mockup">
                        <div class="chart-placeholder">
                            AIOps智能运维大屏
                        </div>
                        <div style="font-size: 0.8rem; color: #666;">
                            全景监控 | 智能告警 | 根因分析 | 链路追踪
                        </div>
                    </div>
                    <div style="margin-top: 10px; font-size: 0.75rem; color: #888; text-align: center;">
                        深色调设计，高对比度图表，专业运维工具界面
                    </div>
                </div>

                <div class="differentiators">
                    <h3>🚀 核心差异化价值</h3>
                    <div class="diff-item">
                        <h4>金融级严苛标准</h4>
                        <p>在银行"金融级"生产环境中千锤百炼，经受"双十一"、年终决算等极端业务洪峰考验</p>
                    </div>
                    <div class="diff-item">
                        <h4>业务与技术深度融合</h4>
                        <p>将技术指标直接转化为业务影响，使技术运维价值被业务部门和管理层直观理解</p>
                    </div>
                    <div class="diff-item">
                        <h4>AIOps实践落地标杆</h4>
                        <p>在智能降噪和根因定位方面算法模型领先，真正解决大型企业"告警风暴"痛点</p>
                    </div>
                    <div class="diff-item">
                        <h4>全栈自研完全掌控</h4>
                        <p>自主研发核心算法，可根据业务变化敏捷迭代，不受外部供应商限制</p>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <div class="experts-section">
                    <h3>👥 行业专家评价</h3>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "风铃系统是中国金融业数字化转型和AIOps实践的标杆案例，在金融行业和科技圈内获得了极高评价。"
                        </div>
                        <div class="quote-author">— 行业分析师评价</div>
                    </div>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "众多银行、证券公司在建设智能运维体系时，都将招行风铃系统作为学习和参考的标杆对象。"
                        </div>
                        <div class="quote-author">— 金融科技专家</div>
                    </div>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "风铃系统是招商银行'科技立行'战略的杰出成果，充分展示了招行在金融科技领域的技术实力。"
                        </div>
                        <div class="quote-author">— IDC、中国信通院等权威机构</div>
                    </div>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "不仅是成功的技术平台，更是先进运维思想和组织文化的体现，对整个行业技术发展起到引领作用。"
                        </div>
                        <div class="quote-author">— QCon、Gdevops技术峰会评价</div>
                    </div>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">👁️</div>
                        <h3 class="feature-title">全景监控</h3>
                        <p class="feature-description">
                            统一纳管基础设施、中间件、数据库、应用系统，从业务维度监控转账成功率、App登录耗时等
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🧠</div>
                        <h3 class="feature-title">智能告警分析</h3>
                        <p class="feature-description">
                            AI算法智能降噪、异常检测、根因分析，将上百个告警自动聚合为1个根源性事件
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔍</div>
                        <h3 class="feature-title">全链路追踪</h3>
                        <p class="feature-description">
                            追踪每次请求经过的每个微服务和组件，形成清晰调用拓扑图，快速定位性能瓶颈
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">容量规划预测</h3>
                        <p class="feature-description">
                            分析历史数据预测业务高峰和资源增长趋势，为扩容规划提供数据支持
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🤖</div>
                        <h3 class="feature-title">自动化自愈</h3>
                        <p class="feature-description">
                            预设自动化处理预案，监测到故障时自动执行重启服务、资源扩容等操作
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">💼</div>
                        <h3 class="feature-title">业务技术融合</h3>
                        <p class="feature-description">
                            将技术指标与业务影响直接挂钩，让技术运维价值被业务部门直观理解
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
