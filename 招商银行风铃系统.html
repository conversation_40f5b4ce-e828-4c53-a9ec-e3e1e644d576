<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>招商银行风铃系统 - 零售客户体验监测平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
            background: linear-gradient(135deg, #c31432 0%, #240b36 100%);
            height: 100vh;
            color: #333;
            overflow: hidden;
        }

        .container {
            height: 100vh;
            display: grid;
            grid-template-rows: auto 1fr;
            background: rgba(255, 255, 255, 0.98);
            backdrop-filter: blur(20px);
        }

        .header {
            background: linear-gradient(135deg, #c31432, #240b36);
            color: white;
            padding: 20px 30px;
            text-align: center;
            position: relative;
        }

        .header::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
            opacity: 0.3;
        }

        .header-content {
            position: relative;
            z-index: 2;
        }

        .logo {
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 8px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .subtitle {
            font-size: 1rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .main-content {
            padding: 20px 30px;
            height: calc(100vh - 120px);
            overflow-y: auto;
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .left-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .right-panel {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .overview {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 15px;
            padding: 20px;
            border-left: 4px solid #c31432;
        }

        .overview h2 {
            color: #c31432;
            font-size: 1.3rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .overview-stats {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 10px;
        }

        .stat-item {
            background: white;
            padding: 15px 10px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 1.5rem;
            font-weight: 700;
            color: #c31432;
            display: block;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.75rem;
            margin-top: 3px;
        }

        .ui-mockup {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            border: 1px solid rgba(195, 20, 50, 0.1);
        }

        .ui-mockup h3 {
            color: #c31432;
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .dashboard-mockup {
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            border-radius: 10px;
            padding: 15px;
            border: 2px dashed #c31432;
            text-align: center;
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
        }

        .dashboard-mockup .chart-placeholder {
            width: 100%;
            height: 120px;
            background: linear-gradient(45deg, #c31432, #240b36);
            border-radius: 8px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
        }

        .experts-section {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
        }

        .experts-section h3 {
            color: #c31432;
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .expert-quote {
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
            padding: 15px;
            border-radius: 10px;
            border-left: 4px solid #c31432;
            margin-bottom: 15px;
        }

        .expert-quote .quote-text {
            font-style: italic;
            color: #495057;
            font-size: 0.9rem;
            line-height: 1.5;
            margin-bottom: 8px;
        }

        .expert-quote .quote-author {
            font-weight: 600;
            color: #c31432;
            font-size: 0.85rem;
        }

        .differentiators {
            background: linear-gradient(135deg, #240b36, #c31432);
            color: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.2);
        }

        .differentiators h3 {
            font-size: 1.2rem;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .diff-item {
            background: rgba(255, 255, 255, 0.1);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 10px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .diff-item h4 {
            font-size: 1rem;
            margin-bottom: 5px;
            color: #fff;
        }

        .diff-item p {
            font-size: 0.85rem;
            opacity: 0.9;
            line-height: 1.4;
        }

        .features-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .feature-card {
            background: white;
            border-radius: 12px;
            padding: 15px;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
            border: 1px solid rgba(195, 20, 50, 0.1);
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
        }

        .feature-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #c31432, #240b36);
        }

        .feature-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .feature-icon {
            width: 35px;
            height: 35px;
            background: linear-gradient(135deg, #c31432, #240b36);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            font-size: 18px;
            color: white;
            box-shadow: 0 2px 8px rgba(195, 20, 50, 0.3);
        }

        .feature-title {
            font-size: 1rem;
            font-weight: 600;
            color: #240b36;
            margin-bottom: 8px;
            line-height: 1.3;
        }

        .feature-description {
            color: #495057;
            line-height: 1.5;
            font-size: 0.85rem;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                padding: 15px 20px;
            }

            .header {
                padding: 15px 20px;
            }

            .logo {
                font-size: 1.8rem;
            }

            .subtitle {
                font-size: 0.9rem;
            }

            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="header-content">
                <h1 class="logo">🔔 风铃系统</h1>
                <p class="subtitle">招商银行零售客户体验监测平台 - 银行业首创的全旅程客户体验数字化管理系统</p>
            </div>
        </div>

        <div class="main-content">
            <div class="left-panel">
                <div class="overview">
                    <h2>🎯 系统概览</h2>
                    <div class="overview-stats">
                        <div class="stat-item">
                            <span class="stat-number">20+</span>
                            <div class="stat-label">打通系统</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">3万+</span>
                            <div class="stat-label">埋点数据</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">1200+</span>
                            <div class="stat-label">体验指标</div>
                        </div>
                        <div class="stat-item">
                            <span class="stat-number">实时</span>
                            <div class="stat-label">监测能力</div>
                        </div>
                    </div>
                </div>

                <div class="ui-mockup">
                    <h3>📱 用户界面展示</h3>
                    <div class="dashboard-mockup">
                        <div class="chart-placeholder">
                            客户体验监测仪表盘
                        </div>
                        <div style="font-size: 0.8rem; color: #666;">
                            实时NPS指标 | 客户旅程分析 | 异常预警
                        </div>
                    </div>
                </div>

                <div class="differentiators">
                    <h3>🚀 核心差异化价值</h3>
                    <div class="diff-item">
                        <h4>银行业首创全旅程监测</h4>
                        <p>业内首个打通20+系统的客户体验监测平台，实现真正的全旅程数字化管理</p>
                    </div>
                    <div class="diff-item">
                        <h4>3万+埋点实时数据采集</h4>
                        <p>行业最大规模的客户行为数据采集网络，覆盖所有客户触点</p>
                    </div>
                    <div class="diff-item">
                        <h4>主观体验客观化量化</h4>
                        <p>将传统主观、定性的体验评价转化为客观、定量、实时的数据分析</p>
                    </div>
                </div>
            </div>

            <div class="right-panel">
                <div class="experts-section">
                    <h3>👥 行业专家评价</h3>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "招商银行的风铃系统代表了银行业客户体验管理的最高水平，通过科技力量实现了客户体验的数字化转型。"
                        </div>
                        <div class="quote-author">— 毕马威银行业客户体验白皮书</div>
                    </div>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "风铃系统的含义就是所有客人一动，我们就能感知到，这是真正以客户为中心的体验管理理念。"
                        </div>
                        <div class="quote-author">— 招商银行副行长 陈昆德</div>
                    </div>
                    <div class="expert-quote">
                        <div class="quote-text">
                            "这不但是传统服务向主动式数字化体验管理的一次改变，更是银行业客户体验管理的重要创新实践。"
                        </div>
                        <div class="quote-author">— 招商银行消保部门负责人</div>
                    </div>
                </div>

                <div class="features-grid">
                    <div class="feature-card">
                        <div class="feature-icon">🔗</div>
                        <h3 class="feature-title">全系统数据打通</h3>
                        <p class="feature-description">
                            整合20多个业务系统，打破数据孤岛，形成完整的客户体验数据链路
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📊</div>
                        <h3 class="feature-title">全旅程体验监测</h3>
                        <p class="feature-description">
                            覆盖手机银行、网银、ATM、网点、客服等全渠道触点的完整体验旅程
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">⚡</div>
                        <h3 class="feature-title">实时数据埋点</h3>
                        <p class="feature-description">
                            3万余个数据埋点实时采集客户行为、交易、服务等多维度数据
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">📈</div>
                        <h3 class="feature-title">多维度指标体系</h3>
                        <p class="feature-description">
                            1200余项体验指标，涵盖NPS、CES、CSAT等核心客户体验指标
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🎛️</div>
                        <h3 class="feature-title">可视化监测仪表盘</h3>
                        <p class="feature-description">
                            实时大屏展示、趋势分析、异常预警、多维度数据钻取分析
                        </p>
                    </div>

                    <div class="feature-card">
                        <div class="feature-icon">🔄</div>
                        <h3 class="feature-title">闭环管理机制</h3>
                        <p class="feature-description">
                            "监测-分析-改进"完整闭环，持续优化客户体验管理流程
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
